---
type: "agent_requested"
description: "Dependency management, package installation, library selection, version management, install package, add dependency, update packages, or upgrade dependencies"
---
# Dependency Management

## Dependency Selection Philosophy

### Prefer Standard Library
- Use built-in/standard library functions when possible
- Avoid external dependencies for simple operations
- Choose standard library solutions for common tasks (file I/O, string manipulation, etc.)

### External Dependencies Criteria
**When external packages are needed, choose based on:**
1. **Popularity**: Most downloaded/starred packages
2. **Stability**: Regular updates and maintenance
3. **Community**: Active community and support
4. **Compatibility**: Works with target platforms and versions

### Version Pinning
**Always pin dependency versions:**

#### Python - requirements.txt
```text
requests==2.31.0
pandas==2.0.3
numpy==1.24.3
```

#### JavaScript - package.json
```json
{
  "dependencies": {
    "express": "^4.18.2",
    "lodash": "^4.17.21"
  }
}
```

#### PowerShell - Module requirements
```powershell
#Requires -Modules @{ModuleName="Az.Accounts"; ModuleVersion="2.12.1"}
```

## Dependency Documentation

### Include Rationale
**Add brief comments explaining why each dependency is needed:**

```python
# requests==2.31.0 - HTTP client for API communications
# pandas==2.0.3 - Data manipulation and analysis
# click==8.1.7 - Command-line interface framework
```

### Security Considerations
- Regularly update dependencies to patch security vulnerabilities
- Use dependency scanning tools when available
- Avoid dependencies with known security issues
- Review dependency licenses for compliance

## Language-Specific Guidelines

### Python
- Use `requirements.txt` for application dependencies
- Use `pyproject.toml` for library/package development
- Consider using `pip-tools` for dependency resolution
- Use virtual environments to isolate dependencies

### JavaScript/TypeScript
- Use `package-lock.json` or `yarn.lock` for reproducible builds
- Separate `dependencies` from `devDependencies`
- Use exact versions for critical dependencies
- Regular security audits with `npm audit`

### PowerShell
- Prefer PowerShell Gallery modules over manual installations
- Use `Install-Module -Force -AllowClobber` for updates
- Test module compatibility with PowerShell 7 before recommending
- Document PowerShell version requirements

## Dependency Hygiene

### Regular Maintenance
- Periodically review and update dependencies
- Remove unused dependencies
- Consolidate overlapping functionality
- Monitor for deprecated packages

### Documentation
- Document critical dependencies in README
- Explain version constraints where relevant
- Provide installation instructions
- Include troubleshooting for common dependency issues