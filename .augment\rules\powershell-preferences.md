---
type: "agent_requested"
description: "PowerShell development, PowerShell scripting, PowerShell modules, or PowerShell preferences"
---

# PowerShell Preferences

## Version Preference
- **Always prefer PowerShell 7** over Windows PowerShell 5.1
- Research module compatibility first - only use Windows PowerShell when specific modules require it
- Use `#Requires -Version 7.0` headers in scripts
- Test cross-platform compatibility when possible

## Naming Conventions
- **Functions**: PascalCase (e.g., `Get-UserData`, `Set-Configuration`)
- **Variables**: camelCase (e.g., `$userName`, `$configPath`)
- **Parameters**: PascalCase (e.g., `-UserName`, `-ConfigPath`)
- **Cmdlet Style**: Use approved verbs (Get-, Set-, New-, Remove-, etc.)

## Script Structure
- Include proper comment-based help for all functions
- Use parameter attributes and validation
- Implement proper error handling with try/catch
- Use Write-Verbose for detailed logging
- Include examples in help documentation

## Module Development
- Follow standard module structure (Public/, Private/, ModuleName.psd1, ModuleName.psm1)
- Export only necessary functions in the module manifest
- Use proper module versioning
- Include comprehensive help documentation
- Test module functionality before deployment

## Best Practices
- Use parameter validation attributes
- Implement proper error handling
- Use approved PowerShell verbs
- Follow PowerShell naming conventions
- Include comprehensive help documentation
- Test cross-platform compatibility when possible