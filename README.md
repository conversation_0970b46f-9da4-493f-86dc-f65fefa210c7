# Augment Setup Script

A PowerShell script for copying the `.augment` folder (containing Augment Code rules) to other directories.

## Overview

This script provides the `Setup-Augment` function that copies the `.augment` folder from the current directory to specified target directories. This is useful for setting up Augment Code rules and configurations in new or existing projects.

## Requirements

- PowerShell 7.0 or later
- Source `.augment` directory must exist in the script's location

## Files

- `Setup-Augment.ps1` - Main script containing the Setup-Augment function
- `Example-Usage.ps1` - Examples demonstrating how to use the function
- `README.md` - This documentation file

## Quick Start

1. **Load the function:**
   ```powershell
   . .\Setup-Augment.ps1
   ```

2. **Copy .augment to a single directory:**
   ```powershell
   Setup-Augment -TargetPath "C:\MyProject"
   ```

3. **Copy .augment to multiple directories:**
   ```powershell
   Setup-Augment -TargetPath @("C:\Project1", "C:\Project2")
   ```

## Function Parameters

### Setup-Augment

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `TargetPath` | string[] | Yes | Target directory or directories where .augment should be copied |
| `Force` | switch | No | Overwrites existing .augment folders in target directories |
| `WhatIf` | switch | No | Shows what would happen without performing the operation |

## Usage Examples

### Basic Usage
```powershell
# Copy to a single directory
Setup-Augment -TargetPath "C:\MyProject"

# Copy to current directory's subdirectory
Setup-Augment -TargetPath ".\NewProject"
```

### Multiple Directories
```powershell
# Copy to multiple directories at once
Setup-Augment -TargetPath @("C:\Project1", "C:\Project2", "C:\Project3")
```

### Overwrite Existing
```powershell
# Force overwrite if .augment already exists
Setup-Augment -TargetPath "C:\ExistingProject" -Force
```

### Preview Changes
```powershell
# See what would happen without actually copying
Setup-Augment -TargetPath "C:\TestProject" -WhatIf
```

### Verbose Output
```powershell
# Get detailed information about the operation
Setup-Augment -TargetPath "C:\MyProject" -Verbose
```

## Features

- ✅ **Validation**: Checks source and target paths before copying
- ✅ **Safety**: Won't overwrite existing .augment folders unless `-Force` is used
- ✅ **Multiple Targets**: Can copy to multiple directories in one command
- ✅ **Preview Mode**: Use `-WhatIf` to see what would happen
- ✅ **Verbose Logging**: Detailed output with `-Verbose` parameter
- ✅ **Error Handling**: Comprehensive error handling and reporting
- ✅ **Summary Report**: Shows success/error/skipped counts after operation

## Error Handling

The script includes comprehensive error handling:

- Validates that the source `.augment` directory exists
- Creates target directories if they don't exist
- Provides clear error messages for failed operations
- Continues processing other targets if one fails
- Displays a summary report at the end

## Getting Help

```powershell
# Get basic help
Get-Help Setup-Augment

# Get detailed help with examples
Get-Help Setup-Augment -Full

# Get help for specific parameters
Get-Help Setup-Augment -Parameter TargetPath
```

## Notes

- The script requires PowerShell 7.0 or later
- Source `.augment` directory must exist in the same location as the script
- Target directories will be created if they don't exist
- Use `-Force` parameter to overwrite existing `.augment` folders
- The function supports PowerShell's common parameters like `-Verbose` and `-WhatIf`

## License

This script is provided as-is for use with Augment Code configurations.
