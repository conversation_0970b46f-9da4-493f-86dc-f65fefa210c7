---
type: "agent_requested"
description: "Architecture design, file organization, modular development, project structure, code organization, create project, build application, develop system, implement solution, or organize code"
---
# Architecture Patterns

## Modular Design Principles

### File Size Management
- HARD LIMIT: 300 lines per file (stop at 250 to reassess)
- NEVER exceed 300 without explicit permission
- Check size every 100 lines during development
- Create logical boundaries between modules
- Use clear import/export patterns

### File Size Enforcement
- Check file size before making edits to large files
- STOP at 250 lines for architecture review
- Propose modular architecture for 5+ function scripts
- Always split files that exceed 300 lines into logical modules
- Use clear import/export patterns between split modules

### Standard Project Structure
Use language/framework conventions:

```text
Python:
src/
├── module_name/
│   ├── __init__.py
│   ├── main.py
│   └── utils/
tests/
requirements.txt
README.md

JavaScript/TypeScript:
src/
├── components/
├── services/
├── utils/
├── index.js
package.json
README.md

PowerShell:
src/
├── ModuleName/
│   ├── ModuleName.psd1
│   ├── ModuleName.psm1
│   └── Public/
│   └── Private/
tests/
README.md
```

### Separation of Concerns
- **Logic Layer**: Business logic and algorithms
- **Data Layer**: Data access and persistence
- **Presentation Layer**: User interface and interaction
- **Configuration Layer**: Settings and environment variables

## User Interface Patterns

### Multi-Operation Tools
**Use menu-driven UI for tools with multiple operations:**
```text
1. Create new project
2. Update existing project
3. Delete project
4. List all projects
0. Exit

Choose an option:
```

### Single-Purpose Scripts
**Skip menus for single-purpose tools:**
- Direct execution with command-line arguments
- Clear usage instructions
- Minimal user interaction required

## Design Patterns

### Dependency Injection
- Inject dependencies rather than hard-coding them
- Use interfaces/abstractions for external services
- Make components testable in isolation

### Configuration Management
- Externalize configuration from code
- Use environment variables for sensitive data
- Provide sensible defaults
- Validate configuration on startup

### Error Boundaries
- Implement proper error boundaries
- Fail fast with clear error messages
- Provide recovery mechanisms where appropriate
- Log errors for debugging

## Scalability Considerations

### Performance
- Optimize for common use cases
- Use appropriate data structures
- Implement caching where beneficial
- Consider memory usage patterns

### Maintainability
- Write self-documenting code
- Use consistent patterns throughout
- Plan for future extensions
- Keep dependencies minimal and up-to-date