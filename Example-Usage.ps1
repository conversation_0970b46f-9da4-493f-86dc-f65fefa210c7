#Requires -Version 7.0

<#
.SYNOPSIS
    Example usage of the Setup-Augment function.

.DESCRIPTION
    This script demonstrates various ways to use the Setup-Augment function
    to copy .augment folders to different target directories.
#>

# Import the Setup-Augment function
. .\Setup-Augment.ps1

Write-Host "=== Augment Setup Examples ===" -ForegroundColor Magenta

# Example 1: Basic usage
Write-Host "`n1. Basic Usage:" -ForegroundColor Yellow
Write-Host "Setup-Augment -TargetPath 'C:\MyProject'"

# Example 2: Multiple directories
Write-Host "`n2. Multiple Directories:" -ForegroundColor Yellow
Write-Host "Setup-Augment -TargetPath @('C:\Project1', 'C:\Project2', 'C:\Project3')"

# Example 3: With Force parameter
Write-Host "`n3. Overwrite Existing:" -ForegroundColor Yellow
Write-Host "Setup-Augment -TargetPath 'C:\ExistingProject' -Force"

# Example 4: With WhatIf parameter
Write-Host "`n4. Preview Changes:" -ForegroundColor Yellow
Write-Host "Setup-Augment -TargetPath 'C:\TestProject' -WhatIf"

# Example 5: Relative paths
Write-Host "`n5. Relative Paths:" -ForegroundColor Yellow
Write-Host "Setup-Augment -TargetPath '.\NewProject'"

# Example 6: With Verbose output
Write-Host "`n6. Verbose Output:" -ForegroundColor Yellow
Write-Host "Setup-Augment -TargetPath 'C:\MyProject' -Verbose"

Write-Host "`n=== Ready to Use ===" -ForegroundColor Green
Write-Host "The Setup-Augment function is now available in this session."
Write-Host "Type 'Get-Help Setup-Augment -Full' for complete documentation."
