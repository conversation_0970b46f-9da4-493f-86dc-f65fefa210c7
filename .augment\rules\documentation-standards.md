---
type: "agent_requested"
description: "Documentation standards, code comments, README files, documentation requirements, write documentation, add comments, or create README"
---
# Documentation Standards

## Auto-Documentation Requirements

### Function Documentation
**Always include language-standard documentation:**

#### Python - Docstrings
```python
def process_data(data: List[str], format_type: str = "json") -> Dict[str, Any]:
    """
    Process input data and return formatted results.
    
    Args:
        data: List of strings to process
        format_type: Output format ('json', 'xml', 'csv')
        
    Returns:
        Dictionary containing processed results
        
    Raises:
        ValueError: If format_type is not supported
    """
```

#### JavaScript/TypeScript - JSDoc
```javascript
/**
 * Process input data and return formatted results
 * @param {string[]} data - Array of strings to process
 * @param {string} formatType - Output format ('json', 'xml', 'csv')
 * @returns {Object} Object containing processed results
 * @throws {Error} If formatType is not supported
 */
```

#### PowerShell - Comment-Based Help
```powershell
<#
.SYNOPSIS
Process input data and return formatted results

.PARAMETER Data
Array of strings to process

.PARAMETER FormatType
Output format ('json', 'xml', 'csv')

.OUTPUTS
Object containing processed results

.EXAMPLE
Process-Data -Data @("item1", "item2") -FormatType "json"
#>
```

### README.md Requirements

#### Essential Sections
1. **Project Description**: What the project does
2. **Installation**: How to install dependencies
3. **Usage**: Basic usage examples
4. **Configuration**: Environment variables or config files
5. **Contributing**: How to contribute (if applicable)

#### README Template
```markdown
# Project Name

Brief description of what this project does.

## Installation

```bash
# Installation commands
```

## Usage

```bash
# Basic usage examples
```

## Configuration

Environment variables or configuration options.

## Examples

Concrete examples with expected outputs.
```

### Inline Comments

#### When to Comment
- Complex algorithms or business logic
- Non-obvious code decisions
- Workarounds or temporary solutions
- Integration points with external systems

#### Comment Style
```python
# WHY: Cache results to avoid expensive API calls during batch processing
cache = {}

# BUSINESS RULE: Orders over $100 get free shipping
if order_total > 100:
    shipping_cost = 0
```

## Documentation Generation

### Automated Generation
- Use tools appropriate for the language/framework
- Generate API documentation from code comments
- Keep generated docs in sync with code changes
- Include code examples in generated documentation

### No Preference Requests
**Never ask about:**
- Documentation format (use language standards)
- Comment style (follow language conventions)
- README structure (use standard sections)
- Documentation tools (use most common for language)