---
type: "agent_requested"
description: "Code standards, file size limits, coding conventions, development practices, code quality, write script, create function, write class, or write method"
---
# Coding Standards

## Language-Specific Conventions

### Python
- **Naming**: snake_case for variables and functions
- **Style**: Follow PEP 8 strictly
- **Type Hints**: Include type hints for all function parameters and return values
- **Structure**: Use src/, tests/, requirements.txt structure
- **Imports**: Group imports (standard library, third-party, local)

### JavaScript/TypeScript
- **Naming**: camelCase for variables and functions, PascalCase for classes
- **Style**: Follow ESLint rules and Prettier formatting
- **Structure**: Use src/, package.json structure
- **Modern Features**: Use ES6+ features, prefer const/let over var

### PowerShell
- **Naming**: PascalCase for functions, camelCase for variables
- **Version**: Always prefer PowerShell 7 over Windows PowerShell 5.1
- **Cmdlet Style**: Use approved verbs (Get-, Set-, New-, Remove-, etc.)
- **Parameters**: Use proper parameter attributes and validation

## Universal Standards

### Meaningful Names
- Use descriptive variable and function names
- Avoid abbreviations unless universally understood
- Use intention-revealing names that explain the "why"

### Error Handling
- Always implement proper error handling
- Use language-specific error handling patterns
- Provide meaningful error messages
- Handle edge cases proactively

### Input Validation
- Validate all inputs at function/method boundaries
- Use type checking where available
- Sanitize inputs for security
- Provide clear feedback for invalid inputs

### Code Comments
- Comment the "why" not the "what"
- Use language-standard documentation formats (docstrings, JSDoc, etc.)
- Keep comments up-to-date with code changes
- Add comments for complex algorithms or business logic

## File Organization

### Size Limits
- Keep files under 400 lines maximum (target: 300 lines)
- Split large files into logical modules when they exceed 400 lines
- Proactively suggest splitting when files approach 300+ lines
- Use clear file naming conventions
- Check file sizes before making significant edits

### Structure
- Follow language/framework standard project structures
- Group related functionality together
- Separate concerns (logic, UI, data, configuration)
- Create clear module boundaries