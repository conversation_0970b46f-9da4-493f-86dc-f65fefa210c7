#Requires -Version 7.0

<#
.SYNOPSIS
    Sets up Augment Code rules in target directories by copying the .augment folder.

.DESCRIPTION
    This script provides the Setup-Augment function that copies the .augment folder
    from the current directory to specified target directories. This is useful for
    setting up Augment Code rules and configurations in new or existing projects.

.EXAMPLE
    Setup-Augment -TargetPath "C:\MyProject"
    Copies .augment folder to C:\MyProject

.EXAMPLE
    Setup-Augment -TargetPath ".\NewProject" -Force
    Copies .augment folder to .\NewProject, overwriting if it exists

.EXAMPLE
    Setup-Augment -TargetPath @("C:\Project1", "C:\Project2")
    Copies .augment folder to multiple directories

.NOTES
    Author: Augment Setup Script
    Requires: PowerShell 7.0+
    
.LINK
    https://augmentcode.com
#>

[CmdletBinding()]
param()

function Copy-AugmentFolder {
    <#
    .SYNOPSIS
        Copies the .augment folder to target directories.

    .DESCRIPTION
        Copies the .augment folder from the current script location to one or more
        target directories. Validates source and target paths before copying.

    .PARAMETER TargetPath
        The target directory or directories where .augment should be copied.
        Can be a single path or an array of paths.

    .PARAMETER Force
        If specified, overwrites existing .augment folders in target directories.

    .PARAMETER Silent
        If specified, suppresses all output except errors.

    .EXAMPLE
        Copy-AugmentFolder -TargetPath "C:\MyProject"

    .EXAMPLE
        Copy-AugmentFolder -TargetPath @("C:\Project1", "C:\Project2") -Force

    .EXAMPLE
        Copy-AugmentFolder -TargetPath ".\NewProject" -Silent
    #>

    [CmdletBinding(SupportsShouldProcess)]
    param(
        [Parameter(Mandatory = $true, Position = 0)]
        [ValidateNotNullOrEmpty()]
        [string[]]$TargetPath,

        [Parameter()]
        [switch]$Force,

        [Parameter()]
        [switch]$Silent
    )
    
    begin {
        if (-not $Silent) { Write-Verbose "Starting Copy-AugmentFolder function" }

        # Get the source .augment directory path
        $scriptRoot = $PSScriptRoot
        if (-not $scriptRoot) {
            $scriptRoot = Get-Location
        }

        $sourceAugmentPath = Join-Path -Path $scriptRoot -ChildPath ".augment"

        # Validate source directory exists
        if (-not (Test-Path -Path $sourceAugmentPath -PathType Container)) {
            $errorMessage = "Source .augment directory not found at: $sourceAugmentPath"
            Write-Error $errorMessage
            throw $errorMessage
        }

        if (-not $Silent) { Write-Verbose "Source .augment directory found at: $sourceAugmentPath" }

        # Initialize counters
        $successCount = 0
        $errorCount = 0
        $skippedCount = 0
    }
    
    process {
        foreach ($target in $TargetPath) {
            try {
                if (-not $Silent) { Write-Verbose "Processing target: $target" }

                # Resolve and validate target path
                $resolvedTarget = Resolve-Path -Path $target -ErrorAction SilentlyContinue
                if (-not $resolvedTarget) {
                    # Try to create the directory if it doesn't exist
                    if ($PSCmdlet.ShouldProcess($target, "Create Directory")) {
                        New-Item -Path $target -ItemType Directory -Force | Out-Null
                        $resolvedTarget = Resolve-Path -Path $target
                    }
                    else {
                        if (-not $Silent) { Write-Warning "Target directory does not exist and -WhatIf is specified: $target" }
                        $skippedCount++
                        continue
                    }
                }

                $targetAugmentPath = Join-Path -Path $resolvedTarget -ChildPath ".augment"

                # Check if target .augment already exists
                if (Test-Path -Path $targetAugmentPath) {
                    if (-not $Force) {
                        if (-not $Silent) { Write-Warning ".augment already exists at $targetAugmentPath. Use -Force to overwrite." }
                        $skippedCount++
                        continue
                    }
                    else {
                        if (-not $Silent) { Write-Verbose "Removing existing .augment directory at: $targetAugmentPath" }
                        if ($PSCmdlet.ShouldProcess($targetAugmentPath, "Remove Existing Directory")) {
                            Remove-Item -Path $targetAugmentPath -Recurse -Force
                        }
                    }
                }

                # Perform the copy operation
                if ($PSCmdlet.ShouldProcess($sourceAugmentPath, "Copy to $targetAugmentPath")) {
                    Copy-Item -Path $sourceAugmentPath -Destination $targetAugmentPath -Recurse -Force
                    if (-not $Silent) { Write-Host "✓ Successfully copied .augment to: $resolvedTarget" -ForegroundColor Green }
                    $successCount++
                }
                else {
                    if (-not $Silent) { Write-Host "Would copy .augment to: $resolvedTarget" -ForegroundColor Yellow }
                    $skippedCount++
                }
            }
            catch {
                Write-Error "Failed to copy .augment to $target`: $($_.Exception.Message)"
                $errorCount++
            }
        }
    }
    
    end {
        # Display summary only if not silent
        if (-not $Silent) {
            Write-Host "`nSetup Summary:" -ForegroundColor Cyan
            Write-Host "  Successful: $successCount" -ForegroundColor Green
            Write-Host "  Errors: $errorCount" -ForegroundColor Red
            Write-Host "  Skipped: $skippedCount" -ForegroundColor Yellow

            if ($errorCount -gt 0) {
                Write-Warning "Some operations failed. Check error messages above."
            }

            Write-Verbose "Copy-AugmentFolder function completed"
        }
    }
}

# Main function that works from any directory
function Setup-Augment {
    <#
    .SYNOPSIS
        Copies .augment folder to target directory.

    .PARAMETER TargetPath
        Optional target path. If not specified, uses current directory.

    .PARAMETER Force
        Overwrites existing .augment folder.
    #>

    [CmdletBinding()]
    param(
        [Parameter(Position = 0)]
        [string]$TargetPath,

        [Parameter()]
        [switch]$Force
    )

    # Use current directory if no target specified
    if (-not $TargetPath) {
        $TargetPath = (Get-Location).Path
    }

    # Find the script directory (where .augment should be)
    $scriptPath = $PSScriptRoot
    if (-not $scriptPath) {
        # If running interactively, try to find the script
        $scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
        if (-not $scriptPath) {
            $scriptPath = "C:\scripts"  # Default fallback
        }
    }

    # Temporarily change to script directory to access .augment
    $originalLocation = Get-Location
    try {
        Set-Location $scriptPath
        Copy-AugmentFolder -TargetPath $TargetPath -Force:$Force -Silent
    }
    finally {
        Set-Location $originalLocation
    }
}

# Export functions for module use
Export-ModuleMember -Function Setup-Augment

# If script is run directly, show help
if ($MyInvocation.InvocationName -ne '.') {
    Write-Host "Augment Setup Script Loaded" -ForegroundColor Green
    Write-Host "Use 'Setup-Augment' to copy .augment folder to current directory" -ForegroundColor Cyan
    Write-Host "Use 'Setup-Augment -TargetPath <path>' to copy to specific directory" -ForegroundColor Cyan
}
