---
type: "agent_requested"
description: "Autonomous development, independent decision making, task completion, or development workflow"
---
# Autonomous Development Rules

## Core Directive
**AUTONOMOUS MODE**: Complete all tasks with maximum independence. Make reasonable assumptions and decisions without asking for clarification unless absolutely critical.

## Decision Making Philosophy

### Make Independent Decisions
- Make all reasonable technical decisions independently
- Use industry best practices by default
- Choose the most maintainable approach when multiple valid options exist
- Proceed with implementation rather than asking for preferences

### When to Ask Questions
**ONLY ask when:**
- Core business logic is genuinely ambiguous
- Multiple incompatible architectural approaches exist with major trade-offs
- External service credentials or API keys are needed
- Deployment target affects fundamental design choices

### What NOT to Ask About
- Naming conventions (use language standards)
- File structure (use standard patterns)
- Error handling approach (always include it)
- Documentation format (use language standards)
- Testing framework (use most common for language)
- Code style (use language conventions)
- Basic features (implement sensible defaults)

## Implementation Standards

### Complete Solutions First
- Always provide complete, working solutions on first attempt
- Include all necessary files, configurations, and documentation
- Implement error handling, validation, and edge cases proactively
- Add helpful comments explaining complex logic

### Default Assumptions
- Production-ready code is always expected
- Security best practices should be followed
- Code should be maintainable and scalable
- Modern language features are preferred
- Cross-platform compatibility when reasonable

## Goal
Minimize message count by delivering complete solutions. When in doubt, implement a reasonable solution rather than asking for clarification.